<?php
require_once 'functions.php';

// Handle AJAX requests
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    header('Content-Type: application/json');

    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'add_task':
                $taskName = $_POST['task_name'] ?? '';
                if (empty(trim($taskName))) {
                    echo json_encode(['success' => false, 'error' => 'Task name cannot be empty']);
                } else {
                    $result = addTask($taskName);
                    if ($result) {
                        echo json_encode(['success' => true, 'message' => 'Task added successfully']);
                    } else {
                        echo json_encode(['success' => false, 'error' => 'Duplicate task or failed to add']);
                    }
                }
                exit;

            case 'toggle_task':
                $taskId = $_POST['task_id'] ?? '';
                $completed = $_POST['completed'] === 'true';
                $result = markTaskAsCompleted($taskId, $completed);
                echo json_encode(['success' => $result]);
                exit;

            case 'delete_task':
                $taskId = $_POST['task_id'] ?? '';
                $result = deleteTask($taskId);
                echo json_encode(['success' => $result]);
                exit;

            case 'subscribe_email':
                $email = $_POST['email'] ?? '';
                if (empty(trim($email))) {
                    echo json_encode(['success' => false, 'error' => 'Email cannot be empty']);
                } else if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
                    echo json_encode(['success' => false, 'error' => 'Invalid email format']);
                } else {
                    $result = subscribeEmail($email);
                    if ($result) {
                        echo json_encode(['success' => true, 'message' => 'Verification email sent! Please check your inbox.']);
                    } else {
                        echo json_encode(['success' => false, 'error' => 'Email already subscribed or failed to send verification']);
                    }
                }
                exit;

            case 'verify_otp':
                $email = $_POST['email'] ?? '';
                $code = $_POST['code'] ?? '';
                $result = verifySubscription($email, $code);
                if ($result) {
                    echo json_encode(['success' => true, 'message' => 'Email verified successfully!']);
                } else {
                    echo json_encode(['success' => false, 'error' => 'Invalid or expired verification code']);
                }
                exit;

            case 'get_tasks':
                $tasks = getAllTasks();
                echo json_encode(['success' => true, 'tasks' => $tasks]);
                exit;
        }
    }
}

// Get tasks for initial load
$tasks = getAllTasks();
$completedTasks = array_filter($tasks, function($task) { return $task['completed']; });
$pendingTasks = array_filter($tasks, function($task) { return !$task['completed']; });
?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TODOS - Professional Task Management</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <style>
        /* Additional styles for task management */
        .task-form {
            background: white;
            padding: 2rem;
            border-radius: 16px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
            margin-bottom: 2rem;
        }

        .task-input-wrapper {
            display: flex;
            gap: 1rem;
            align-items: center;
        }

        .task-input {
            flex: 1;
            padding: 12px 16px;
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            font-size: 1rem;
            transition: all 0.3s ease;
        }

        .task-input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .tasks-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .task-item {
            background: #fafbfc;
            border: 1px solid #f1f5f9;
            border-radius: 12px;
            padding: 1rem;
            margin-bottom: 0.75rem;
            display: flex;
            align-items: center;
            gap: 1rem;
            transition: all 0.3s ease;
        }

        .task-item:hover {
            background: #f8fafc;
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
        }

        .task-item.completed {
            opacity: 0.7;
            background: #f0fdf4;
            border-color: #bbf7d0;
        }

        .task-item.completed .task-text {
            text-decoration: line-through;
            color: #6b7280;
        }

        .task-status {
            width: 20px;
            height: 20px;
            cursor: pointer;
        }

        .task-text {
            flex: 1;
            font-weight: 500;
            color: #1e293b;
        }

        .delete-task {
            background: #ef4444;
            color: white;
            border: none;
            padding: 6px 12px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 0.875rem;
            transition: all 0.3s ease;
        }

        .delete-task:hover {
            background: #dc2626;
            transform: scale(1.05);
        }

        .loading {
            opacity: 0.6;
            pointer-events: none;
        }

        /* OTP Modal Styles */
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10000;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
        }

        .modal-overlay.active {
            opacity: 1;
            visibility: visible;
        }

        .modal {
            background: white;
            padding: 2rem;
            border-radius: 16px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
            max-width: 400px;
            width: 90%;
            transform: scale(0.9);
            transition: transform 0.3s ease;
        }

        .modal-overlay.active .modal {
            transform: scale(1);
        }

        .otp-input {
            width: 100%;
            padding: 12px;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            font-size: 1.2rem;
            text-align: center;
            letter-spacing: 0.5em;
            margin: 1rem 0;
        }

        .otp-input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
    </style>
</head>

<body>
    <!-- Background Gradient Orbs -->
    <div class="bg-orbs">
        <div class="orb orb-1"></div>
        <div class="orb orb-2"></div>
        <div class="orb orb-3"></div>
        <div class="orb orb-4"></div>
    </div>

    <!-- Navbar -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-brand">
                <h1>TODOS</h1>
            </div>
            <div class="nav-menu">
                <a href="#" class="nav-link">About Us</a>
                <a href="#" class="nav-link">Service</a>
                <a href="#" class="nav-link">Our Team</a>
                <button class="contact-btn">Contact Us</button>
            </div>
            <div class="mobile-menu-toggle">
                <span></span>
                <span></span>
                <span></span>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero">
        <div class="hero-container">
            <div class="hero-content">
                <h1 class="hero-title">
                    An Agency That<br>
                    Specializes In Digital<br>
                    <span class="gradient-text">Task Management</span>
                </h1>
                <p class="hero-subtitle">
                    Streamline your workflow with our powerful task management platform.
                    Built for teams that value efficiency and collaboration.
                </p>
                <div class="hero-cta">
                    <button class="cta-primary">Get Started</button>
                    <button class="cta-secondary">Watch Demo</button>
                </div>
            </div>
            <div class="stats-card">
                <div class="stat-item">
                    <div class="stat-number" id="completedTasks"><?php echo count($completedTasks); ?></div>
                    <div class="stat-label">Completed Tasks</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" id="pendingTasks"><?php echo count($pendingTasks); ?></div>
                    <div class="stat-label">Pending Tasks</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" id="totalTasks"><?php echo count($tasks); ?></div>
                    <div class="stat-label">Total Tasks</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" id="totalUsers"><?php echo count(getSubscribers()); ?></div>
                    <div class="stat-label">Active Users</div>
                </div>
            </div>
        </div>
    </section>

    <!-- Todo List Section -->
    <section class="todo-section">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">Task Management</h2>
                <p class="section-subtitle">Stay organized and boost your productivity</p>
            </div>

            <!-- Add Task Form -->
            <div class="task-form">
                <h3 style="margin-bottom: 1rem; color: #1e293b;">Add New Task</h3>
                <form id="add-task-form" class="task-input-wrapper">
                    <input type="text" name="task-name" id="task-name" class="task-input" placeholder="Enter new task" required>
                    <button type="submit" id="add-task" class="cta-primary">Add Task</button>
                </form>
            </div>

            <div class="todo-container">
                <div class="todo-header">
                    <div class="todo-tabs">
                        <button class="tab-btn" data-filter="all">All Tasks</button>
                        <button class="tab-btn active" data-filter="pending">Pending</button>
                        <button class="tab-btn" data-filter="completed">Completed</button>
                    </div>
                </div>

                <div class="todo-content">
                    <div class="todo-title-section">
                        <div>
                            <h3 class="todo-title">Today's Tasks</h3>
                            <p class="todo-date"><?php echo date('l, j F'); ?></p>
                        </div>
                    </div>

                    <div class="task-filters">
                        <button class="filter-btn active" data-filter="all">
                            All <span class="count"><?php echo count($tasks); ?></span>
                        </button>
                        <button class="filter-btn" data-filter="pending">
                            Pending <span class="count"><?php echo count($pendingTasks); ?></span>
                        </button>
                        <button class="filter-btn" data-filter="completed">
                            Completed <span class="count"><?php echo count($completedTasks); ?></span>
                        </button>
                    </div>

                    <!-- Tasks List -->
                    <ul class="tasks-list" id="tasks-list">
                        <?php if (empty($tasks)): ?>
                            <li class="task-item" style="text-align: center; color: #64748b; font-style: italic;">
                                No tasks yet. Add your first task above!
                            </li>
                        <?php else: ?>
                            <?php foreach ($tasks as $task): ?>
                                <li class="task-item <?php echo $task['completed'] ? 'completed' : ''; ?>" data-task-id="<?php echo htmlspecialchars($task['id']); ?>" data-status="<?php echo $task['completed'] ? 'completed' : 'pending'; ?>">
                                    <input type="checkbox" class="task-status" <?php echo $task['completed'] ? 'checked' : ''; ?>>
                                    <span class="task-text"><?php echo htmlspecialchars($task['name']); ?></span>
                                    <button class="delete-task">Delete</button>
                                </li>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </ul>
                </div>
            </div>
        </div>
    </section>

    <!-- Newsletter Section -->
    <section class="newsletter-section">
        <div class="container">
            <div class="newsletter-container">
                <div class="newsletter-icon">
                    <div class="icon-wrapper">
                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"></path>
                            <polyline points="22,6 12,13 2,6"></polyline>
                        </svg>
                        <div class="heart-badge">
                            <svg viewBox="0 0 24 24" fill="currentColor">
                                <path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"></path>
                            </svg>
                        </div>
                    </div>
                </div>
                <h2 class="newsletter-title">Subscribe to Task Reminders</h2>
                <p class="newsletter-description">
                    Get hourly email reminders for your pending tasks. Never miss an important task again!
                </p>
                <form id="subscription-form" class="newsletter-form">
                    <div class="input-wrapper">
                        <input type="email" name="email" placeholder="<EMAIL>" class="newsletter-input" required>
                        <button type="submit" id="submit-email" class="newsletter-btn">Subscribe</button>
                    </div>
                </form>
                <p class="newsletter-promise">We promise not to spam you!</p>
            </div>
        </div>
    </section>

    <!-- OTP Verification Modal -->
    <div id="otp-modal" class="modal-overlay">
        <div class="modal">
            <h3 style="text-align: center; margin-bottom: 1rem; color: #1e293b;">Verify Your Email</h3>
            <p style="text-align: center; color: #64748b; margin-bottom: 1.5rem;">
                We've sent a 6-digit verification code to your email. Please enter it below:
            </p>
            <form id="otp-form">
                <input type="text" id="otp-input" class="otp-input" placeholder="000000" maxlength="6" pattern="[0-9]{6}" required>
                <div style="display: flex; gap: 1rem; margin-top: 1.5rem;">
                    <button type="button" id="cancel-otp" class="cta-secondary" style="flex: 1;">Cancel</button>
                    <button type="submit" id="verify-otp" class="cta-primary" style="flex: 1;">Verify</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Toast Container -->
    <div id="toast-container" style="position: fixed; bottom: 20px; right: 20px; z-index: 10001;"></div>

    <script src="script.js"></script>
    <script>
        // Task Management JavaScript
        let currentEmail = '';

        // Add task form handler
        document.getElementById('add-task-form').addEventListener('submit', async function(e) {
            e.preventDefault();
            const taskName = document.getElementById('task-name').value.trim();
            const button = document.getElementById('add-task');

            if (!taskName) {
                showToast('Please enter a task name', 'error');
                return;
            }

            button.classList.add('loading');
            button.textContent = 'Adding...';

            try {
                const response = await fetch('', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                    body: `action=add_task&task_name=${encodeURIComponent(taskName)}`
                });

                const result = await response.json();

                if (result.success) {
                    showToast(result.message, 'success');
                    document.getElementById('task-name').value = '';
                    await refreshTasks();
                } else {
                    showToast(result.error, 'error');
                }
            } catch (error) {
                showToast('Failed to add task', 'error');
            } finally {
                button.classList.remove('loading');
                button.textContent = 'Add Task';
            }
        });

        // Task status change handler
        document.addEventListener('change', async function(e) {
            if (e.target.classList.contains('task-status')) {
                const taskItem = e.target.closest('.task-item');
                const taskId = taskItem.dataset.taskId;
                const completed = e.target.checked;

                try {
                    const response = await fetch('', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                        body: `action=toggle_task&task_id=${encodeURIComponent(taskId)}&completed=${completed}`
                    });

                    const result = await response.json();

                    if (result.success) {
                        if (completed) {
                            taskItem.classList.add('completed');
                            taskItem.dataset.status = 'completed';
                            showToast('Task completed!', 'success');
                        } else {
                            taskItem.classList.remove('completed');
                            taskItem.dataset.status = 'pending';
                            showToast('Task marked as pending', 'success');
                        }
                        await refreshStats();
                    } else {
                        e.target.checked = !completed; // Revert checkbox
                        showToast('Failed to update task', 'error');
                    }
                } catch (error) {
                    e.target.checked = !completed; // Revert checkbox
                    showToast('Failed to update task', 'error');
                }
            }
        });

        // Task delete handler
        document.addEventListener('click', async function(e) {
            if (e.target.classList.contains('delete-task')) {
                const taskItem = e.target.closest('.task-item');
                const taskId = taskItem.dataset.taskId;

                if (!confirm('Are you sure you want to delete this task?')) {
                    return;
                }

                try {
                    const response = await fetch('', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                        body: `action=delete_task&task_id=${encodeURIComponent(taskId)}`
                    });

                    const result = await response.json();

                    if (result.success) {
                        taskItem.remove();
                        showToast('Task deleted successfully', 'success');
                        await refreshStats();
                    } else {
                        showToast('Failed to delete task', 'error');
                    }
                } catch (error) {
                    showToast('Failed to delete task', 'error');
                }
            }
        });

        // Subscription form handler
        document.getElementById('subscription-form').addEventListener('submit', async function(e) {
            e.preventDefault();
            const email = document.querySelector('input[name="email"]').value.trim();
            const button = document.getElementById('submit-email');

            if (!email) {
                showToast('Please enter your email address', 'error');
                return;
            }

            button.classList.add('loading');
            button.textContent = 'Subscribing...';

            try {
                const response = await fetch('', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                    body: `action=subscribe_email&email=${encodeURIComponent(email)}`
                });

                const result = await response.json();

                if (result.success) {
                    showToast(result.message, 'success');
                    currentEmail = email;
                    document.getElementById('otp-modal').classList.add('active');
                    document.getElementById('otp-input').focus();
                } else {
                    showToast(result.error, 'error');
                }
            } catch (error) {
                showToast('Failed to subscribe', 'error');
            } finally {
                button.classList.remove('loading');
                button.textContent = 'Subscribe';
            }
        });

        // OTP verification handlers
        document.getElementById('otp-form').addEventListener('submit', async function(e) {
            e.preventDefault();
            const code = document.getElementById('otp-input').value.trim();
            const button = document.getElementById('verify-otp');

            if (code.length !== 6) {
                showToast('Please enter a 6-digit code', 'error');
                return;
            }

            button.classList.add('loading');
            button.textContent = 'Verifying...';

            try {
                const response = await fetch('', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                    body: `action=verify_otp&email=${encodeURIComponent(currentEmail)}&code=${encodeURIComponent(code)}`
                });

                const result = await response.json();

                if (result.success) {
                    showToast(result.message, 'success');
                    document.getElementById('otp-modal').classList.remove('active');
                    document.getElementById('otp-input').value = '';
                    document.querySelector('input[name="email"]').value = '';
                    await refreshStats();
                } else {
                    showToast(result.error, 'error');
                }
            } catch (error) {
                showToast('Failed to verify code', 'error');
            } finally {
                button.classList.remove('loading');
                button.textContent = 'Verify';
            }
        });

        document.getElementById('cancel-otp').addEventListener('click', function() {
            document.getElementById('otp-modal').classList.remove('active');
            document.getElementById('otp-input').value = '';
        });

        // Filter functionality
        document.querySelectorAll('.filter-btn, .tab-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                const filter = this.dataset.filter;
                if (!filter) return;

                // Update active states
                document.querySelectorAll('.filter-btn, .tab-btn').forEach(b => b.classList.remove('active'));
                this.classList.add('active');

                // Filter tasks
                const tasks = document.querySelectorAll('.task-item[data-task-id]');
                tasks.forEach(task => {
                    const status = task.dataset.status;
                    if (filter === 'all' || status === filter) {
                        task.style.display = 'flex';
                    } else {
                        task.style.display = 'none';
                    }
                });
            });
        });

        // Utility functions
        async function refreshTasks() {
            try {
                const response = await fetch('', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                    body: 'action=get_tasks'
                });

                const result = await response.json();

                if (result.success) {
                    const tasksList = document.getElementById('tasks-list');
                    tasksList.innerHTML = '';

                    if (result.tasks.length === 0) {
                        tasksList.innerHTML = '<li class="task-item" style="text-align: center; color: #64748b; font-style: italic;">No tasks yet. Add your first task above!</li>';
                    } else {
                        result.tasks.forEach(task => {
                            const li = document.createElement('li');
                            li.className = `task-item ${task.completed ? 'completed' : ''}`;
                            li.dataset.taskId = task.id;
                            li.dataset.status = task.completed ? 'completed' : 'pending';
                            li.innerHTML = `
                                <input type="checkbox" class="task-status" ${task.completed ? 'checked' : ''}>
                                <span class="task-text">${escapeHtml(task.name)}</span>
                                <button class="delete-task">Delete</button>
                            `;
                            tasksList.appendChild(li);
                        });
                    }

                    await refreshStats();
                }
            } catch (error) {
                console.error('Failed to refresh tasks:', error);
            }
        }

        async function refreshStats() {
            try {
                const response = await fetch('', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                    body: 'action=get_tasks'
                });

                const result = await response.json();

                if (result.success) {
                    const tasks = result.tasks;
                    const completed = tasks.filter(t => t.completed).length;
                    const pending = tasks.filter(t => !t.completed).length;
                    const total = tasks.length;

                    updateStatWithAnimation('completedTasks', completed);
                    updateStatWithAnimation('pendingTasks', pending);
                    updateStatWithAnimation('totalTasks', total);

                    // Update filter counts
                    document.querySelector('.filter-btn[data-filter="all"] .count').textContent = total;
                    document.querySelector('.filter-btn[data-filter="pending"] .count').textContent = pending;
                    document.querySelector('.filter-btn[data-filter="completed"] .count').textContent = completed;
                }
            } catch (error) {
                console.error('Failed to refresh stats:', error);
            }
        }

        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        function showToast(message, type) {
            const toast = document.createElement('div');
            toast.className = `toast ${type}`;
            toast.textContent = message;
            toast.style.cssText = `
                background: ${type === 'success' ? '#10b981' : '#ef4444'};
                color: white;
                padding: 12px 20px;
                border-radius: 12px;
                box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
                margin-bottom: 10px;
                transform: translateX(400px);
                transition: transform 0.3s ease;
                font-weight: 500;
            `;

            const container = document.getElementById('toast-container');
            container.appendChild(toast);

            setTimeout(() => {
                toast.style.transform = 'translateX(0)';
            }, 100);

            setTimeout(() => {
                toast.style.transform = 'translateX(400px)';
                setTimeout(() => {
                    toast.remove();
                }, 300);
            }, 3000);
        }
    </script>
</body>
</html>
