<?php
require_once 'functions.php';

$message = '';
$messageType = '';
$unsubscribed = false;

// Handle unsubscription
if (isset($_GET['email']) && isset($_GET['token'])) {
    $email = $_GET['email'];
    $token = $_GET['token'];

    // Verify token
    if (verifyUnsubscribeToken($email, $token)) {
        // Attempt to unsubscribe
        if (unsubscribeEmail($email)) {
            $message = 'You have been successfully unsubscribed from task reminders. You will no longer receive email notifications.';
            $messageType = 'success';
            $unsubscribed = true;
        } else {
            $message = 'Email address not found in our subscription list. You may have already been unsubscribed.';
            $messageType = 'warning';
        }
    } else {
        $message = 'Invalid unsubscribe link. Please use the link provided in your email.';
        $messageType = 'error';
    }
} else {
    $message = 'Invalid unsubscribe request. Please use the link provided in your email.';
    $messageType = 'error';
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Unsubscribe - TODOS</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <style>
        .unsubscribe-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 2rem;
        }

        .unsubscribe-card {
            background: white;
            padding: 3rem;
            border-radius: 24px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.08);
            border: 1px solid rgba(0, 0, 0, 0.05);
            max-width: 500px;
            width: 100%;
            text-align: center;
        }

        .unsubscribe-icon {
            width: 80px;
            height: 80px;
            margin: 0 auto 2rem;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
        }

        .unsubscribe-icon.success {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            color: white;
        }

        .unsubscribe-icon.warning {
            background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
            color: white;
        }

        .unsubscribe-icon.error {
            background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
            color: white;
        }

        .unsubscribe-message {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 2rem;
        }

        .unsubscribe-message.success {
            color: #059669;
        }

        .unsubscribe-message.warning {
            color: #d97706;
        }

        .unsubscribe-message.error {
            color: #dc2626;
        }

        .action-buttons {
            display: flex;
            gap: 1rem;
            justify-content: center;
            flex-wrap: wrap;
        }

        .back-button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 12px;
            font-weight: 600;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        .back-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }

        .resubscribe-button {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 12px;
            font-weight: 600;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3);
        }

        .resubscribe-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(16, 185, 129, 0.4);
        }
    </style>
</head>
<body>
    <!-- Background Gradient Orbs -->
    <div class="bg-orbs">
        <div class="orb orb-1"></div>
        <div class="orb orb-2"></div>
        <div class="orb orb-3"></div>
        <div class="orb orb-4"></div>
    </div>

    <div class="unsubscribe-container">
        <div class="unsubscribe-card">
            <div class="unsubscribe-icon <?php echo $messageType; ?>">
                <?php if ($unsubscribed): ?>
                    ✓
                <?php elseif ($messageType === 'warning'): ?>
                    !
                <?php else: ?>
                    ✗
                <?php endif; ?>
            </div>

            <!-- Do not modify the ID of the heading -->
            <h2 id="unsubscription-heading">Unsubscribe from Task Updates</h2>

            <p class="unsubscribe-message <?php echo $messageType; ?>">
                <?php echo htmlspecialchars($message); ?>
            </p>

            <div class="action-buttons">
                <a href="index.php" class="back-button">
                    Go to Task Manager
                </a>

                <?php if ($unsubscribed): ?>
                    <a href="index.php#newsletter" class="resubscribe-button">
                        Resubscribe
                    </a>
                <?php endif; ?>
            </div>
        </div>
    </div>
</body>
</html>
