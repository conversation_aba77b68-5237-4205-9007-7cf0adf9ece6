<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TODOS - Professional Task Management</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Background Gradient Orbs -->
    <div class="bg-orbs">
        <div class="orb orb-1"></div>
        <div class="orb orb-2"></div>
        <div class="orb orb-3"></div>
        <div class="orb orb-4"></div>
    </div>

    <!-- Navbar -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-brand">
                <h1>TODOS</h1>
            </div>
            <div class="nav-menu">
                <a href="#" class="nav-link">About Us</a>
                <a href="#" class="nav-link">Service</a>
                <a href="#" class="nav-link">Our Team</a>
                <button class="contact-btn">Contact Us</button>
            </div>
            <div class="mobile-menu-toggle">
                <span></span>
                <span></span>
                <span></span>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero">
        <div class="hero-container">
            <div class="hero-content">
                <h1 class="hero-title">
                    An Agency That<br>
                    Specializes In Digital<br>
                    <span class="gradient-text">Task Management</span>
                </h1>
                <p class="hero-subtitle">
                    Streamline your workflow with our powerful task management platform. 
                    Built for teams that value efficiency and collaboration.
                </p>
                <div class="hero-cta">
                    <button class="cta-primary">Get Started</button>
                    <button class="cta-secondary">Watch Demo</button>
                </div>
            </div>
            <div class="stats-card">
                <div class="stat-item">
                    <div class="stat-number" id="completedTasks">45</div>
                    <div class="stat-label">Completed Tasks</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" id="pendingTasks">12</div>
                    <div class="stat-label">Pending Tasks</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" id="totalTasks">57</div>
                    <div class="stat-label">Total Tasks</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" id="totalUsers">8</div>
                    <div class="stat-label">Active Users</div>
                </div>
            </div>
        </div>
    </section>

    <!-- Todo List Section -->
    <section class="todo-section">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">Task Management</h2>
                <p class="section-subtitle">Stay organized and boost your productivity</p>
            </div>
            
            <div class="todo-container">
                <div class="todo-header">
                    <div class="todo-tabs">
                        <button class="tab-btn">Messages</button>
                        <button class="tab-btn active">Today's Task</button>
                        <button class="tab-btn">Last Activity</button>
                    </div>
                </div>
                
                <div class="todo-content">
                    <div class="todo-title-section">
                        <div>
                            <h3 class="todo-title">Today's Task</h3>
                            <p class="todo-date">Wednesday, 11 May</p>
                        </div>
                        <button class="new-task-btn">
                            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <line x1="12" y1="5" x2="12" y2="19"></line>
                                <line x1="5" y1="12" x2="19" y2="12"></line>
                            </svg>
                            New Task
                        </button>
                    </div>

                    <div class="task-filters">
                        <button class="filter-btn active">
                            All <span class="count">35</span>
                        </button>
                        <button class="filter-btn">
                            Open <span class="count">14</span>
                        </button>
                        <button class="filter-btn">
                            Closed <span class="count">19</span>
                        </button>
                        <button class="filter-btn">
                            Archived <span class="count">2</span>
                        </button>
                    </div>

                    <div class="task-list">
                        <div class="task-item">
                            <div class="task-info">
                                <h4 class="task-name">Client Review & Feedback</h4>
                                <p class="task-project">Crypto Wallet Redesign</p>
                                <div class="task-meta">
                                    <span class="task-time">Today 10:00 PM - 11:45 PM</span>
                                </div>
                            </div>
                            <div class="task-right">
                                <div class="task-status completed">
                                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                        <polyline points="20,6 9,17 4,12"></polyline>
                                    </svg>
                                </div>
                                <div class="task-avatars">
                                    <div class="avatar avatar-1"></div>
                                    <div class="avatar avatar-2"></div>
                                </div>
                            </div>
                        </div>

                        <div class="task-item">
                            <div class="task-info">
                                <h4 class="task-name">Create Wireframe</h4>
                                <p class="task-project">Crypto Wallet Redesign</p>
                                <div class="task-meta">
                                    <span class="task-time">Today 09:15 PM - 10:00 PM</span>
                                </div>
                            </div>
                            <div class="task-right">
                                <div class="task-status completed">
                                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                        <polyline points="20,6 9,17 4,12"></polyline>
                                    </svg>
                                </div>
                                <div class="task-avatars">
                                    <div class="avatar avatar-3"></div>
                                    <div class="avatar avatar-4"></div>
                                    <div class="avatar avatar-5"></div>
                                    <div class="avatar-more">+4</div>
                                </div>
                            </div>
                        </div>

                        <div class="task-item">
                            <div class="task-info">
                                <h4 class="task-name">Design System Updates</h4>
                                <p class="task-project">Mobile App Redesign</p>
                                <div class="task-meta">
                                    <span class="task-time">Tomorrow 02:00 PM - 04:30 PM</span>
                                </div>
                            </div>
                            <div class="task-right">
                                <div class="task-status pending">
                                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                        <circle cx="12" cy="12" r="10"></circle>
                                        <polyline points="12,6 12,12 16,14"></polyline>
                                    </svg>
                                </div>
                                <div class="task-avatars">
                                    <div class="avatar avatar-6"></div>
                                    <div class="avatar avatar-7"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Newsletter Section -->
    <section class="newsletter-section">
        <div class="container">
            <div class="newsletter-container">
                <div class="newsletter-icon">
                    <div class="icon-wrapper">
                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"></path>
                            <polyline points="22,6 12,13 2,6"></polyline>
                        </svg>
                        <div class="heart-badge">
                            <svg viewBox="0 0 24 24" fill="currentColor">
                                <path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"></path>
                            </svg>
                        </div>
                    </div>
                </div>
                <h2 class="newsletter-title">Subscribe Newsletter</h2>
                <p class="newsletter-description">
                    You will never miss our podcasts, latest news etc. Our newsletter is once a week, every Thursday.
                </p>
                <form class="newsletter-form">
                    <div class="input-wrapper">
                        <input type="email" placeholder="<EMAIL>" class="newsletter-input">
                        <button type="submit" class="newsletter-btn">Subscribe</button>
                    </div>
                </form>
                <p class="newsletter-promise">We promise not to spam you!</p>
            </div>
        </div>
    </section>

    <script src="script.js"></script>
</body>
</html>