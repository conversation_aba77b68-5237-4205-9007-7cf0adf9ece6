<?php

/**
 * Adds a new task to the task list
 *
 * @param string $task_name The name of the task to add.
 * @return bool True on success, false on failure.
 */
function addTask( string $task_name ): bool {
	$file  = __DIR__ . '/tasks.txt';

	try {
		// Validate input
		$task_name = trim($task_name);
		if (empty($task_name)) {
			return false;
		}

		// Read existing tasks
		$tasks = [];
		if (file_exists($file)) {
			$content = file_get_contents($file);
			if (!empty($content) && $content !== "// Store the data in JSON format.\n") {
				$tasks = json_decode($content, true) ?: [];
			}
		}

		// Check for duplicates (case-insensitive)
		foreach ($tasks as $task) {
			if (strcasecmp($task['name'], $task_name) === 0) {
				return false; // Duplicate task
			}
		}

		// Create new task
		$newTask = [
			'id' => uniqid('task_', true),
			'name' => $task_name,
			'completed' => false
		];

		// Add to tasks array
		$tasks[] = $newTask;

		// Save to file
		return file_put_contents($file, json_encode($tasks, JSON_PRETTY_PRINT)) !== false;

	} catch (Exception $e) {
		error_log("Error adding task: " . $e->getMessage());
		return false;
	}
}

/**
 * Retrieves all tasks from the tasks.txt file
 *
 * @return array Array of tasks. -- Format [ id, name, completed ]
 */
function getAllTasks(): array {
	$file = __DIR__ . '/tasks.txt';

	try {
		if (!file_exists($file)) {
			return [];
		}

		$content = file_get_contents($file);
		if (empty($content) || $content === "// Store the data in JSON format.\n") {
			return [];
		}

		$tasks = json_decode($content, true);
		return is_array($tasks) ? $tasks : [];

	} catch (Exception $e) {
		error_log("Error getting tasks: " . $e->getMessage());
		return [];
	}
}

/**
 * Marks a task as completed or uncompleted
 *
 * @param string  $task_id The ID of the task to mark.
 * @param bool $is_completed True to mark as completed, false to mark as uncompleted.
 * @return bool True on success, false on failure
 */
function markTaskAsCompleted( string $task_id, bool $is_completed ): bool {
	$file  = __DIR__ . '/tasks.txt';

	try {
		$tasks = getAllTasks();
		$found = false;

		// Find and update the task
		for ($i = 0; $i < count($tasks); $i++) {
			if ($tasks[$i]['id'] === $task_id) {
				$tasks[$i]['completed'] = $is_completed;
				$found = true;
				break;
			}
		}

		if (!$found) {
			return false;
		}

		// Save updated tasks
		return file_put_contents($file, json_encode($tasks, JSON_PRETTY_PRINT)) !== false;

	} catch (Exception $e) {
		error_log("Error marking task: " . $e->getMessage());
		return false;
	}
}

/**
 * Deletes a task from the task list
 *
 * @param string $task_id The ID of the task to delete.
 * @return bool True on success, false on failure.
 */
function deleteTask( string $task_id ): bool {
	$file  = __DIR__ . '/tasks.txt';

	try {
		$tasks = getAllTasks();
		$originalCount = count($tasks);

		// Filter out the task to delete
		$tasks = array_filter($tasks, function($task) use ($task_id) {
			return $task['id'] !== $task_id;
		});

		// Re-index array
		$tasks = array_values($tasks);

		// Check if task was found and removed
		if (count($tasks) === $originalCount) {
			return false; // Task not found
		}

		// Save updated tasks
		return file_put_contents($file, json_encode($tasks, JSON_PRETTY_PRINT)) !== false;

	} catch (Exception $e) {
		error_log("Error deleting task: " . $e->getMessage());
		return false;
	}
}

/**
 * Generates a 6-digit verification code
 *
 * @return string The generated verification code.
 */
function generateVerificationCode(): string {
	return sprintf('%06d', mt_rand(100000, 999999));
}

/**
 * Subscribe an email address to task notifications.
 *
 * Generates a verification code, stores the pending subscription,
 * and sends a verification email to the subscriber.
 *
 * @param string $email The email address to subscribe.
 * @return bool True if verification email sent successfully, false otherwise.
 */
function subscribeEmail( string $email ): bool {
	$file = __DIR__ . '/pending_subscriptions.txt';

	try {
		// Validate email
		$email = trim(strtolower($email));
		if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
			return false;
		}

		// Check if already subscribed
		$subscribers = getSubscribers();
		if (in_array($email, $subscribers)) {
			return false; // Already subscribed
		}

		// Read existing pending subscriptions
		$pending = [];
		if (file_exists($file)) {
			$content = file_get_contents($file);
			if (!empty($content) && !str_contains($content, '// Store the data in JSON format')) {
				$pending = json_decode($content, true) ?: [];
			}
		}

		// Generate verification code
		$code = generateVerificationCode();
		$timestamp = time();

		// Store pending subscription
		$pending[$email] = [
			'code' => $code,
			'timestamp' => $timestamp
		];

		// Save to file
		if (file_put_contents($file, json_encode($pending, JSON_PRETTY_PRINT)) === false) {
			return false;
		}

		// Send verification email
		return sendVerificationEmail($email, $code);

	} catch (Exception $e) {
		error_log("Error subscribing email: " . $e->getMessage());
		return false;
	}
}

/**
 * Verifies an email subscription
 *
 * @param string $email The email address to verify.
 * @param string $code The verification code.
 * @return bool True on success, false on failure.
 */
function verifySubscription( string $email, string $code ): bool {
	$pending_file     = __DIR__ . '/pending_subscriptions.txt';
	$subscribers_file = __DIR__ . '/subscribers.txt';

	try {
		$email = trim(strtolower($email));
		$code = trim($code);

		// Read pending subscriptions
		if (!file_exists($pending_file)) {
			return false;
		}

		$content = file_get_contents($pending_file);
		if (empty($content) || str_contains($content, '// Store the data in JSON format')) {
			return false;
		}

		$pending = json_decode($content, true) ?: [];

		// Check if email exists in pending
		if (!isset($pending[$email])) {
			return false;
		}

		$subscription = $pending[$email];

		// Check if code matches
		if ($subscription['code'] !== $code) {
			return false;
		}

		// Check if not expired (5 minutes = 300 seconds)
		if (time() - $subscription['timestamp'] > 300) {
			// Remove expired entry
			unset($pending[$email]);
			file_put_contents($pending_file, json_encode($pending, JSON_PRETTY_PRINT));
			return false;
		}

		// Add to subscribers
		$subscribers = getSubscribers();
		if (!in_array($email, $subscribers)) {
			$subscribers[] = $email;
			if (file_put_contents($subscribers_file, json_encode($subscribers, JSON_PRETTY_PRINT)) === false) {
				return false;
			}
		}

		// Remove from pending
		unset($pending[$email]);
		file_put_contents($pending_file, json_encode($pending, JSON_PRETTY_PRINT));

		return true;

	} catch (Exception $e) {
		error_log("Error verifying subscription: " . $e->getMessage());
		return false;
	}
}

/**
 * Unsubscribes an email from the subscribers list
 *
 * @param string $email The email address to unsubscribe.
 * @return bool True on success, false on failure.
 */
function unsubscribeEmail( string $email ): bool {
	$subscribers_file = __DIR__ . '/subscribers.txt';

	try {
		$email = trim(strtolower($email));
		$subscribers = getSubscribers();

		// Find and remove the email
		$key = array_search($email, $subscribers);
		if ($key === false) {
			return false; // Email not found
		}

		unset($subscribers[$key]);
		$subscribers = array_values($subscribers); // Re-index array

		// Save updated subscribers
		return file_put_contents($subscribers_file, json_encode($subscribers, JSON_PRETTY_PRINT)) !== false;

	} catch (Exception $e) {
		error_log("Error unsubscribing email: " . $e->getMessage());
		return false;
	}
}

/**
 * Sends task reminders to all subscribers
 * Internally calls  sendTaskEmail() for each subscriber
 */
function sendTaskReminders(): void {
	try {
		$subscribers = getSubscribers();
		$tasks = getAllTasks();

		// Filter pending tasks
		$pendingTasks = array_filter($tasks, function($task) {
			return !$task['completed'];
		});

		// Only send if there are pending tasks
		if (empty($pendingTasks)) {
			return;
		}

		// Send to each subscriber
		foreach ($subscribers as $email) {
			sendTaskEmail($email, $pendingTasks);
		}

	} catch (Exception $e) {
		error_log("Error sending task reminders: " . $e->getMessage());
	}
}

/**
 * Sends a task reminder email to a subscriber with pending tasks.
 *
 * @param string $email The email address of the subscriber.
 * @param array $pending_tasks Array of pending tasks to include in the email.
 * @return bool True if email was sent successfully, false otherwise.
 */
function sendTaskEmail( string $email, array $pending_tasks ): bool {
	$subject = 'Task Planner - Pending Tasks Reminder';

	try {
		if (empty($pending_tasks)) {
			return false;
		}

		// Generate unsubscribe token
		$unsubscribeToken = generateUnsubscribeToken($email);
		$unsubscribeLink = "http://" . $_SERVER['HTTP_HOST'] . dirname($_SERVER['PHP_SELF']) . "/unsubscribe.php?email=" . urlencode($email) . "&token=" . urlencode($unsubscribeToken);

		// Build HTML email body
		$body = '<h2>Pending Tasks Reminder</h2>';
		$body .= '<p>Here are the current pending tasks:</p>';
		$body .= '<ul>';

		foreach ($pending_tasks as $task) {
			$body .= '<li>' . htmlspecialchars($task['name']) . '</li>';
		}

		$body .= '</ul>';
		$body .= '<p><a id="unsubscribe-link" href="' . $unsubscribeLink . '">Unsubscribe from notifications</a></p>';

		// Email headers
		$headers = [
			'MIME-Version: 1.0',
			'Content-type: text/html; charset=UTF-8',
			'From: <EMAIL>',
			'Reply-To: <EMAIL>',
			'X-Mailer: PHP/' . phpversion()
		];

		// Send email
		return mail($email, $subject, $body, implode("\r\n", $headers));

	} catch (Exception $e) {
		error_log("Error sending task email: " . $e->getMessage());
		return false;
	}
}

/**
 * Helper function to get all subscribers
 *
 * @return array Array of subscriber email addresses
 */
function getSubscribers(): array {
	$file = __DIR__ . '/subscribers.txt';

	try {
		if (!file_exists($file)) {
			return [];
		}

		$content = file_get_contents($file);
		if (empty($content)) {
			return [];
		}

		$subscribers = json_decode($content, true);
		return is_array($subscribers) ? $subscribers : [];

	} catch (Exception $e) {
		error_log("Error getting subscribers: " . $e->getMessage());
		return [];
	}
}

/**
 * Sends verification email to subscriber
 *
 * @param string $email The email address
 * @param string $code The verification code
 * @return bool True if email sent successfully
 */
function sendVerificationEmail(string $email, string $code): bool {
	try {
		$subject = 'Verify subscription to Task Planner';
		$verificationLink = "http://" . $_SERVER['HTTP_HOST'] . dirname($_SERVER['PHP_SELF']) . "/verify.php?email=" . urlencode($email) . "&code=" . urlencode($code);

		$body = '<p>Click the link below to verify your subscription to Task Planner:</p>';
		$body .= '<p><a id="verification-link" href="' . $verificationLink . '">Verify Subscription</a></p>';

		$headers = [
			'MIME-Version: 1.0',
			'Content-type: text/html; charset=UTF-8',
			'From: <EMAIL>',
			'Reply-To: <EMAIL>',
			'X-Mailer: PHP/' . phpversion()
		];

		return mail($email, $subject, $body, implode("\r\n", $headers));

	} catch (Exception $e) {
		error_log("Error sending verification email: " . $e->getMessage());
		return false;
	}
}

/**
 * Generates unsubscribe token for email
 *
 * @param string $email The email address
 * @return string The generated token
 */
function generateUnsubscribeToken(string $email): string {
	$secret = 'task_planner_secret_key_2024'; // In production, use environment variable
	return hash_hmac('sha256', $email, $secret);
}

/**
 * Verifies unsubscribe token
 *
 * @param string $email The email address
 * @param string $token The token to verify
 * @return bool True if token is valid
 */
function verifyUnsubscribeToken(string $email, string $token): bool {
	$expectedToken = generateUnsubscribeToken($email);
	return hash_equals($expectedToken, $token);
}
