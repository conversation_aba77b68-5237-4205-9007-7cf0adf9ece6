#!/bin/bash

# Task Scheduler CRON Job Setup Script
# This script sets up an hourly CRON job to send task reminders

echo "Setting up CRON job for Task Scheduler..."

# Get the current directory (where the script is located)
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
CRON_PHP_FILE="$SCRIPT_DIR/cron.php"

# Check if cron.php exists
if [ ! -f "$CRON_PHP_FILE" ]; then
    echo "Error: cron.php not found in $SCRIPT_DIR"
    exit 1
fi

# Define the CRON job command
# Run every hour at minute 0
CRON_COMMAND="0 * * * * /usr/bin/php $CRON_PHP_FILE"

# Check if PHP is available
if ! command -v php &> /dev/null; then
    echo "Error: PHP is not installed or not in PATH"
    echo "Please install PHP and try again"
    exit 1
fi

# Get current user's crontab
TEMP_CRON=$(mktemp)
crontab -l 2>/dev/null > "$TEMP_CRON"

# Check if the CRON job already exists
if grep -Fq "$CRON_PHP_FILE" "$TEMP_CRON"; then
    echo "CRON job for Task Scheduler already exists!"
    echo "Existing entry:"
    grep "$CRON_PHP_FILE" "$TEMP_CRON"

    # Ask user if they want to update it
    read -p "Do you want to update the existing CRON job? (y/n): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        # Remove existing entries
        grep -v "$CRON_PHP_FILE" "$TEMP_CRON" > "${TEMP_CRON}.new"
        mv "${TEMP_CRON}.new" "$TEMP_CRON"
        echo "Removed existing CRON job entry"
    else
        echo "CRON job setup cancelled"
        rm "$TEMP_CRON"
        exit 0
    fi
fi

# Add the new CRON job
echo "$CRON_COMMAND" >> "$TEMP_CRON"

# Install the new crontab
if crontab "$TEMP_CRON"; then
    echo "✓ CRON job successfully added!"
    echo "✓ Task reminders will be sent every hour"
    echo "✓ CRON job command: $CRON_COMMAND"

    # Test the PHP script
    echo ""
    echo "Testing the CRON script..."
    if php "$CRON_PHP_FILE"; then
        echo "✓ CRON script test successful"
    else
        echo "⚠ Warning: CRON script test failed"
        echo "Please check the PHP script for errors"
    fi

    echo ""
    echo "Setup complete! The CRON job is now active."
    echo "To view all CRON jobs, run: crontab -l"
    echo "To remove this CRON job, run: crontab -e"
else
    echo "✗ Failed to install CRON job"
    echo "Please check your system permissions and try again"
    rm "$TEMP_CRON"
    exit 1
fi

# Clean up
rm "$TEMP_CRON"

echo ""
echo "CRON Job Details:"
echo "- Frequency: Every hour (at minute 0)"
echo "- Script: $CRON_PHP_FILE"
echo "- Command: $CRON_COMMAND"
echo ""
echo "The system will now automatically send task reminders to all subscribers every hour."