<?php
require_once 'functions.php';

$message = '';
$messageType = '';
$verified = false;

// Handle verification
if (isset($_GET['email']) && isset($_GET['code'])) {
    $email = $_GET['email'];
    $code = $_GET['code'];

    if (verifySubscription($email, $code)) {
        $message = 'Your email has been successfully verified! You will now receive task reminders.';
        $messageType = 'success';
        $verified = true;
    } else {
        $message = 'Invalid or expired verification code. Please try subscribing again.';
        $messageType = 'error';
    }
} else {
    $message = 'Invalid verification link. Please check your email for the correct link.';
    $messageType = 'error';
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Email Verification - TODOS</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <style>
        .verification-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 2rem;
        }

        .verification-card {
            background: white;
            padding: 3rem;
            border-radius: 24px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.08);
            border: 1px solid rgba(0, 0, 0, 0.05);
            max-width: 500px;
            width: 100%;
            text-align: center;
        }

        .verification-icon {
            width: 80px;
            height: 80px;
            margin: 0 auto 2rem;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
        }

        .verification-icon.success {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            color: white;
        }

        .verification-icon.error {
            background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
            color: white;
        }

        .verification-message {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 2rem;
        }

        .verification-message.success {
            color: #059669;
        }

        .verification-message.error {
            color: #dc2626;
        }

        .back-button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 12px;
            font-weight: 600;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        .back-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }
    </style>
</head>
<body>
    <!-- Background Gradient Orbs -->
    <div class="bg-orbs">
        <div class="orb orb-1"></div>
        <div class="orb orb-2"></div>
        <div class="orb orb-3"></div>
        <div class="orb orb-4"></div>
    </div>

    <div class="verification-container">
        <div class="verification-card">
            <div class="verification-icon <?php echo $messageType; ?>">
                <?php if ($verified): ?>
                    ✓
                <?php else: ?>
                    ✗
                <?php endif; ?>
            </div>

            <!-- Do not modify the ID of the heading -->
            <h2 id="verification-heading">Subscription Verification</h2>

            <p class="verification-message <?php echo $messageType; ?>">
                <?php echo htmlspecialchars($message); ?>
            </p>

            <a href="index.php" class="back-button">
                <?php echo $verified ? 'Go to Task Manager' : 'Try Again'; ?>
            </a>
        </div>
    </div>
</body>
</html>