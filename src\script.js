// Dynamic stats counter with smooth animation
function animateCounter(element, target, duration = 2000) {
    const start = 0;
    const increment = target / (duration / 16);
    let current = start;
    
    const timer = setInterval(() => {
        current += increment;
        if (current >= target) {
            current = target;
            clearInterval(timer);
        }
        element.textContent = Math.floor(current);
    }, 16);
}

function updateStatWithAnimation(elementId, newValue) {
    const element = document.getElementById(elementId);
    const currentValue = parseInt(element.textContent);
    
    if (currentValue !== newValue) {
        element.style.transform = 'scale(1.1)';
        element.style.color = '#667eea';
        
        setTimeout(() => {
            element.textContent = newValue;
            element.style.transform = 'scale(1)';
            element.style.color = '';
        }, 150);
    }
}

// Initialize counters when page loads
document.addEventListener('DOMContentLoaded', function() {
    // Simulate dynamic data (in real app, this would come from API)
    const stats = {
        completed: 45,
        pending: 12,
        total: 57,
        users: 8
    };
    
    // Animate counters with staggered timing
    setTimeout(() => {
        animateCounter(document.getElementById('completedTasks'), stats.completed);
    }, 300);
    
    setTimeout(() => {
        animateCounter(document.getElementById('pendingTasks'), stats.pending);
    }, 500);
    
    setTimeout(() => {
        animateCounter(document.getElementById('totalTasks'), stats.total);
    }, 700);
    
    setTimeout(() => {
        animateCounter(document.getElementById('totalUsers'), stats.users);
    }, 900);
    
    // Update stats every 30 seconds (simulate real-time updates)
    setInterval(() => {
        // Simulate small changes in stats
        stats.completed += Math.floor(Math.random() * 3);
        stats.pending = Math.max(0, stats.pending + Math.floor(Math.random() * 3) - 1);
        stats.total = stats.completed + stats.pending;
        stats.users += Math.floor(Math.random() * 2);
        
        // Update display
        updateStatWithAnimation('completedTasks', stats.completed);
        updateStatWithAnimation('pendingTasks', stats.pending);
        updateStatWithAnimation('totalTasks', stats.total);
        updateStatWithAnimation('totalUsers', stats.users);
    }, 30000);
});

// Tab functionality with smooth transitions
document.querySelectorAll('.tab-btn').forEach(btn => {
    btn.addEventListener('click', function() {
        // Remove active class from all tabs
        document.querySelectorAll('.tab-btn').forEach(b => {
            b.classList.remove('active');
        });
        
        // Add active class to clicked tab
        this.classList.add('active');
        
        // Add subtle animation
        this.style.transform = 'scale(0.95)';
        setTimeout(() => {
            this.style.transform = 'scale(1)';
        }, 100);
    });
});

// Filter functionality with visual feedback
document.querySelectorAll('.filter-btn').forEach(btn => {
    btn.addEventListener('click', function() {
        // Remove active class from all filters
        document.querySelectorAll('.filter-btn').forEach(b => {
            b.classList.remove('active');
        });
        
        // Add active class to clicked filter
        this.classList.add('active');
        
        // Add ripple effect
        const ripple = document.createElement('span');
        ripple.classList.add('ripple');
        this.appendChild(ripple);
        
        setTimeout(() => {
            ripple.remove();
        }, 600);
        
        // Here you would filter tasks based on selection
        filterTasks(this.textContent.trim().split(' ')[0].toLowerCase());
    });
});

function filterTasks(filter) {
    const tasks = document.querySelectorAll('.task-item');
    
    tasks.forEach((task, index) => {
        // Add staggered animation
        task.style.opacity = '0';
        task.style.transform = 'translateY(20px)';
        
        setTimeout(() => {
            task.style.opacity = '1';
            task.style.transform = 'translateY(0)';
        }, index * 100);
    });
    
    console.log(`Filtering tasks by: ${filter}`);
}

// Newsletter form with enhanced UX
document.querySelector('.newsletter-form').addEventListener('submit', function(e) {
    e.preventDefault();
    const email = document.querySelector('.newsletter-input').value;
    const btn = document.querySelector('.newsletter-btn');
    const input = document.querySelector('.newsletter-input');
    
    if (email && isValidEmail(email)) {
        // Show loading state
        const originalText = btn.textContent;
        btn.textContent = 'Subscribing...';
        btn.disabled = true;
        btn.style.background = 'linear-gradient(135deg, #94a3b8 0%, #64748b 100%)';
        
        // Simulate API call
        setTimeout(() => {
            btn.textContent = '✓ Subscribed!';
            btn.style.background = 'linear-gradient(135deg, #10b981 0%, #059669 100%)';
            input.value = '';
            
            // Show success message
            showNotification('Successfully subscribed to newsletter!', 'success');
            
            // Reset button after delay
            setTimeout(() => {
                btn.textContent = originalText;
                btn.disabled = false;
                btn.style.background = 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)';
            }, 3000);
        }, 1500);
    } else {
        // Show error state
        input.style.borderColor = '#ef4444';
        input.style.boxShadow = '0 0 0 3px rgba(239, 68, 68, 0.1)';
        showNotification('Please enter a valid email address', 'error');
        
        setTimeout(() => {
            input.style.borderColor = '';
            input.style.boxShadow = '';
        }, 3000);
    }
});

function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

function showNotification(message, type) {
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.textContent = message;
    notification.style.cssText = `
        position: fixed;
        top: 100px;
        right: 20px;
        background: ${type === 'success' ? '#10b981' : '#ef4444'};
        color: white;
        padding: 12px 20px;
        border-radius: 12px;
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        z-index: 10000;
        transform: translateX(400px);
        transition: transform 0.3s ease;
    `;
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
        notification.style.transform = 'translateX(0)';
    }, 100);
    
    setTimeout(() => {
        notification.style.transform = 'translateX(400px)';
        setTimeout(() => {
            notification.remove();
        }, 300);
    }, 3000);
}

// Mobile menu toggle
document.querySelector('.mobile-menu-toggle').addEventListener('click', function() {
    this.classList.toggle('active');
    
    // Add mobile menu styles
    if (!document.querySelector('#mobile-menu-styles')) {
        const style = document.createElement('style');
        style.id = 'mobile-menu-styles';
        style.textContent = `
            .mobile-menu-toggle.active span:nth-child(1) {
                transform: rotate(-45deg) translate(-5px, 6px);
            }
            .mobile-menu-toggle.active span:nth-child(2) {
                opacity: 0;
            }
            .mobile-menu-toggle.active span:nth-child(3) {
                transform: rotate(45deg) translate(-5px, -6px);
            }
        `;
        document.head.appendChild(style);
    }
});

// Smooth scrolling for navigation links
document.querySelectorAll('.nav-link').forEach(link => {
    link.addEventListener('click', function(e) {
        e.preventDefault();
        // Add smooth scrolling functionality here
    });
});

// Add scroll effect to navbar
let lastScrollTop = 0;
window.addEventListener('scroll', function() {
    const navbar = document.querySelector('.navbar');
    const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
    
    if (scrollTop > 100) {
        navbar.style.background = 'rgba(255, 255, 255, 0.98)';
        navbar.style.boxShadow = '0 4px 20px rgba(0, 0, 0, 0.08)';
    } else {
        navbar.style.background = 'rgba(255, 255, 255, 0.95)';
        navbar.style.boxShadow = 'none';
    }
    
    // Hide/show navbar on scroll
    if (scrollTop > lastScrollTop && scrollTop > 200) {
        navbar.style.transform = 'translateY(-100%)';
    } else {
        navbar.style.transform = 'translateY(0)';
    }
    
    lastScrollTop = scrollTop;
});

// Enhanced task item interactions
document.querySelectorAll('.task-item').forEach(item => {
    item.addEventListener('mouseenter', function() {
        this.style.transform = 'translateY(-4px)';
    });
    
    item.addEventListener('mouseleave', function() {
        this.style.transform = 'translateY(0)';
    });
    
    item.addEventListener('click', function() {
        // Add selection state
        document.querySelectorAll('.task-item').forEach(t => t.classList.remove('selected'));
        this.classList.add('selected');
        
        // Add CSS for selected state
        if (!document.querySelector('#dynamic-styles')) {
            const style = document.createElement('style');
            style.id = 'dynamic-styles';
            style.textContent = `
                .task-item.selected {
                    border-color: #667eea !important;
                    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.15) !important;
                    background: #f8fafc !important;
                }
            `;
            document.head.appendChild(style);
        }
    });
});

// New task button with enhanced interaction
document.querySelector('.new-task-btn').addEventListener('click', function() {
    // Add click animation
    this.style.transform = 'scale(0.95)';
    
    setTimeout(() => {
        this.style.transform = 'scale(1)';
    }, 150);
    
    // Show modal or navigate (placeholder)
    showNotification('New task creation coming soon!', 'success');
    console.log('Create new task clicked');
});

// Intersection Observer for scroll animations
const observerOptions = {
    threshold: 0.1,
    rootMargin: '0px 0px -50px 0px'
};

const observer = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
        if (entry.isIntersecting) {
            entry.target.style.opacity = '1';
            entry.target.style.transform = 'translateY(0)';
        }
    });
}, observerOptions);

// Observe elements for scroll animations
document.querySelectorAll('.stats-card, .todo-container, .newsletter-container').forEach(element => {
    element.style.opacity = '0';
    element.style.transform = 'translateY(30px)';
    element.style.transition = 'opacity 0.8s ease, transform 0.8s ease';
    observer.observe(element);
});

// Add loading animation
window.addEventListener('load', function() {
    document.body.style.opacity = '0';
    document.body.style.transition = 'opacity 0.5s ease';

    setTimeout(() => {
        document.body.style.opacity = '1';
    }, 100);
});

// Enhanced task management functionality
document.addEventListener('DOMContentLoaded', function() {
    // Auto-refresh stats every 30 seconds if on index.php
    if (window.location.pathname.includes('index.php') || window.location.pathname.endsWith('/')) {
        setInterval(async function() {
            if (typeof refreshStats === 'function') {
                await refreshStats();
            }
        }, 30000);
    }

    // Add keyboard shortcuts
    document.addEventListener('keydown', function(e) {
        // Ctrl/Cmd + Enter to add task
        if ((e.ctrlKey || e.metaKey) && e.key === 'Enter') {
            const taskInput = document.getElementById('task-name');
            if (taskInput && taskInput.value.trim()) {
                document.getElementById('add-task-form').dispatchEvent(new Event('submit'));
            }
        }

        // Escape to close modal
        if (e.key === 'Escape') {
            const modal = document.getElementById('otp-modal');
            if (modal && modal.classList.contains('active')) {
                modal.classList.remove('active');
                document.getElementById('otp-input').value = '';
            }
        }
    });

    // Auto-focus OTP input when modal opens
    const otpModal = document.getElementById('otp-modal');
    if (otpModal) {
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
                    if (otpModal.classList.contains('active')) {
                        setTimeout(() => {
                            document.getElementById('otp-input').focus();
                        }, 100);
                    }
                }
            });
        });
        observer.observe(otpModal, { attributes: true });
    }

    // Format OTP input
    const otpInput = document.getElementById('otp-input');
    if (otpInput) {
        otpInput.addEventListener('input', function(e) {
            // Only allow numbers
            this.value = this.value.replace(/[^0-9]/g, '');

            // Auto-submit when 6 digits entered
            if (this.value.length === 6) {
                setTimeout(() => {
                    document.getElementById('otp-form').dispatchEvent(new Event('submit'));
                }, 500);
            }
        });

        otpInput.addEventListener('paste', function(e) {
            e.preventDefault();
            const paste = (e.clipboardData || window.clipboardData).getData('text');
            const numbers = paste.replace(/[^0-9]/g, '').substring(0, 6);
            this.value = numbers;

            if (numbers.length === 6) {
                setTimeout(() => {
                    document.getElementById('otp-form').dispatchEvent(new Event('submit'));
                }, 500);
            }
        });
    }

    // Enhanced task item animations
    function animateTaskItem(element, type = 'add') {
        if (type === 'add') {
            element.style.opacity = '0';
            element.style.transform = 'translateY(20px) scale(0.95)';
            element.style.transition = 'all 0.3s ease';

            setTimeout(() => {
                element.style.opacity = '1';
                element.style.transform = 'translateY(0) scale(1)';
            }, 50);
        } else if (type === 'remove') {
            element.style.transition = 'all 0.3s ease';
            element.style.opacity = '0';
            element.style.transform = 'translateX(-100%) scale(0.8)';

            setTimeout(() => {
                element.remove();
            }, 300);
        }
    }

    // Add ripple effect to buttons
    function addRippleEffect(button) {
        button.addEventListener('click', function(e) {
            const ripple = document.createElement('span');
            const rect = this.getBoundingClientRect();
            const size = Math.max(rect.width, rect.height);
            const x = e.clientX - rect.left - size / 2;
            const y = e.clientY - rect.top - size / 2;

            ripple.style.cssText = `
                position: absolute;
                width: ${size}px;
                height: ${size}px;
                left: ${x}px;
                top: ${y}px;
                background: rgba(255, 255, 255, 0.3);
                border-radius: 50%;
                transform: scale(0);
                animation: ripple 0.6s ease-out;
                pointer-events: none;
            `;

            // Add ripple animation CSS if not exists
            if (!document.querySelector('#ripple-styles')) {
                const style = document.createElement('style');
                style.id = 'ripple-styles';
                style.textContent = `
                    @keyframes ripple {
                        to {
                            transform: scale(2);
                            opacity: 0;
                        }
                    }
                `;
                document.head.appendChild(style);
            }

            this.style.position = 'relative';
            this.style.overflow = 'hidden';
            this.appendChild(ripple);

            setTimeout(() => {
                ripple.remove();
            }, 600);
        });
    }

    // Add ripple effect to all buttons
    document.querySelectorAll('button, .cta-primary, .cta-secondary, .contact-btn, .newsletter-btn').forEach(addRippleEffect);

    // Smooth scroll to sections
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function(e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
});

// Global utility functions for task management
window.TaskManager = {
    showToast: function(message, type = 'info') {
        if (typeof showToast === 'function') {
            showToast(message, type);
        } else {
            console.log(`${type.toUpperCase()}: ${message}`);
        }
    },

    refreshTasks: async function() {
        if (typeof refreshTasks === 'function') {
            await refreshTasks();
        }
    },

    refreshStats: async function() {
        if (typeof refreshStats === 'function') {
            await refreshStats();
        }
    }
};