* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Inter', sans-serif;
  background: #ffffff;
  color: #1a1a1a;
  line-height: 1.6;
  overflow-x: hidden;
  position: relative;
}

/* Background Gradient Orbs */
.bg-orbs {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: -1;
}

.orb {
  position: absolute;
  border-radius: 50%;
  filter: blur(80px);
  opacity: 0.6;
  animation: float 20s infinite ease-in-out;
}

.orb-1 {
  width: 400px;
  height: 400px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  top: -200px;
  right: -200px;
  animation-delay: 0s;
}

.orb-2 {
  width: 300px;
  height: 300px;
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  bottom: -150px;
  left: -150px;
  animation-delay: -10s;
}

.orb-3 {
  width: 250px;
  height: 250px;
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  animation-delay: -5s;
}

.orb-4 {
  width: 200px;
  height: 200px;
  background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
  top: 20%;
  left: 10%;
  animation-delay: -15s;
}

@keyframes float {

  0%,
  100% {
    transform: translateY(0px) rotate(0deg);
  }

  33% {
    transform: translateY(-30px) rotate(120deg);
  }

  66% {
    transform: translateY(20px) rotate(240deg);
  }
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
}

/* Navbar */
.navbar {
  position: fixed;
  top: 0;
  width: 100%;
  z-index: 1000;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.nav-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 80px;
}

.nav-brand h1 {
  font-size: 2rem;
  font-weight: 800;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  letter-spacing: -0.02em;
}

.nav-menu {
  display: flex;
  align-items: center;
  gap: 2rem;
}

.nav-link {
  color: #64748b;
  text-decoration: none;
  font-weight: 500;
  font-size: 0.95rem;
  transition: color 0.3s ease;
  position: relative;
}

.nav-link:hover {
  color: #1e293b;
}

.nav-link::after {
  content: '';
  position: absolute;
  width: 0;
  height: 2px;
  bottom: -4px;
  left: 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  transition: width 0.3s ease;
}

.nav-link:hover::after {
  width: 100%;
}

.contact-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 12px;
  font-weight: 600;
  font-size: 0.95rem;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.contact-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.mobile-menu-toggle {
  display: none;
  flex-direction: column;
  cursor: pointer;
}

.mobile-menu-toggle span {
  width: 25px;
  height: 3px;
  background: #1e293b;
  margin: 3px 0;
  transition: 0.3s;
  border-radius: 2px;
}

/* Hero Section */
.hero {
  padding: 120px 0 100px;
  min-height: 100vh;
  display: flex;
  align-items: center;
}

.hero-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
  display: grid;
  grid-template-columns: 1fr 400px;
  gap: 4rem;
  align-items: center;
}

.hero-title {
  font-size: 4rem;
  font-weight: 800;
  line-height: 1.1;
  color: #1e293b;
  margin-bottom: 1.5rem;
  letter-spacing: -0.02em;
}

.gradient-text {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.hero-subtitle {
  font-size: 1.25rem;
  color: #64748b;
  margin-bottom: 2.5rem;
  line-height: 1.6;
  max-width: 500px;
}

.hero-cta {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.cta-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 16px 32px;
  border-radius: 12px;
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.cta-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.cta-secondary {
  background: transparent;
  color: #64748b;
  border: 2px solid #e2e8f0;
  padding: 14px 32px;
  border-radius: 12px;
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.cta-secondary:hover {
  border-color: #667eea;
  color: #667eea;
  transform: translateY(-2px);
}

.stats-card {
  background: white;
  padding: 2.5rem;
  border-radius: 24px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(0, 0, 0, 0.05);
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
}

.stat-item {
  text-align: center;
}

.stat-number {
  font-size: 2.5rem;
  font-weight: 800;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 0.5rem;
}

.stat-label {
  font-size: 0.9rem;
  color: #64748b;
  font-weight: 500;
  line-height: 1.3;
}

/* Section Headers */
.section-header {
  text-align: center;
  margin-bottom: 4rem;
}

.section-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: #1e293b;
  margin-bottom: 1rem;
}

.section-subtitle {
  font-size: 1.1rem;
  color: #64748b;
  max-width: 600px;
  margin: 0 auto;
}

/* Todo Section */
.todo-section {
  padding: 100px 0;
}

.todo-container {
  background: white;
  border-radius: 24px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(0, 0, 0, 0.05);
  padding: 2.5rem;
  max-width: 900px;
  margin: 0 auto;
}

.todo-header {
  margin-bottom: 2rem;
  border-bottom: 1px solid #f1f5f9;
}

.todo-tabs {
  display: flex;
  gap: 0;
}

.tab-btn {
  background: none;
  border: none;
  padding: 1rem 1.5rem;
  color: #64748b;
  font-weight: 500;
  cursor: pointer;
  position: relative;
  transition: all 0.3s ease;
  border-radius: 8px 8px 0 0;
}

.tab-btn:hover {
  color: #1e293b;
  background: #f8fafc;
}

.tab-btn.active {
  color: #667eea;
  background: #f8fafc;
}

.tab-btn.active::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 2px 2px 0 0;
}

.todo-title-section {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 2rem;
}

.todo-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: #1e293b;
  margin-bottom: 0.5rem;
}

.todo-date {
  color: #64748b;
  font-size: 0.95rem;
}

.new-task-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 12px 20px;
  border-radius: 12px;
  font-weight: 600;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.new-task-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.new-task-btn svg {
  width: 16px;
  height: 16px;
}

.task-filters {
  display: flex;
  gap: 0.75rem;
  margin-bottom: 2rem;
  flex-wrap: wrap;
}

.filter-btn {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  color: #64748b;
  padding: 8px 16px;
  border-radius: 20px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.3s ease;
  font-size: 0.9rem;
  font-weight: 500;
}

.filter-btn:hover {
  background: #f1f5f9;
  border-color: #cbd5e1;
}

.filter-btn.active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-color: transparent;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.count {
  background: rgba(255, 255, 255, 0.2);
  padding: 2px 8px;
  border-radius: 10px;
  font-size: 0.8rem;
  font-weight: 600;
}

.filter-btn:not(.active) .count {
  background: #e2e8f0;
  color: #64748b;
}

.task-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.task-item {
  background: #fafbfc;
  border: 1px solid #f1f5f9;
  border-radius: 16px;
  padding: 1.5rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: all 0.3s ease;
}

.task-item:hover {
  background: #f8fafc;
  border-color: #e2e8f0;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
}

.task-name {
  font-size: 1.1rem;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 0.5rem;
}

.task-project {
  color: #64748b;
  font-size: 0.9rem;
  margin-bottom: 0.5rem;
}

.task-time {
  color: #94a3b8;
  font-size: 0.85rem;
}

.task-right {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.task-status {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.task-status.completed {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
  box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3);
}

.task-status.pending {
  background: #fef3c7;
  color: #d97706;
  border: 1px solid #fde68a;
}

.task-status svg {
  width: 16px;
  height: 16px;
}

.task-avatars {
  display: flex;
  align-items: center;
  margin-left: -8px;
}

.avatar {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  border: 3px solid white;
  margin-left: -8px;
  position: relative;
}

.avatar:first-child {
  margin-left: 0;
}

.avatar-1 {
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
}

.avatar-2 {
  background: linear-gradient(135deg, #4ecdc4 0%, #44a08d 100%);
}

.avatar-3 {
  background: linear-gradient(135deg, #45b7d1 0%, #96c93d 100%);
}

.avatar-4 {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.avatar-5 {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.avatar-6 {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.avatar-7 {
  background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
}

.avatar-more {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background: #e2e8f0;
  color: #64748b;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.8rem;
  font-weight: 600;
  margin-left: -8px;
  border: 3px solid white;
}

/* Newsletter Section */
.newsletter-section {
  padding: 100px 0;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
}

.newsletter-container {
  background: white;
  border-radius: 24px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(0, 0, 0, 0.05);
  padding: 4rem 3rem;
  text-align: center;
  max-width: 600px;
  margin: 0 auto;
}

.newsletter-icon {
  margin-bottom: 2rem;
}

.icon-wrapper {
  position: relative;
  display: inline-block;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 1.5rem;
  border-radius: 20px;
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.icon-wrapper svg {
  width: 32px;
  height: 32px;
  color: white;
}

.heart-badge {
  position: absolute;
  top: -8px;
  right: -8px;
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  border-radius: 50%;
  padding: 8px;
  box-shadow: 0 4px 15px rgba(240, 147, 251, 0.4);
  border: 3px solid white;
}

.heart-badge svg {
  width: 14px;
  height: 14px;
  color: white;
}

.newsletter-title {
  font-size: 2rem;
  font-weight: 700;
  color: #1e293b;
  margin-bottom: 1rem;
}

.newsletter-description {
  color: #64748b;
  line-height: 1.6;
  margin-bottom: 2.5rem;
  font-size: 1.05rem;
}

.newsletter-form {
  margin-bottom: 1.5rem;
}

.input-wrapper {
  display: flex;
  background: #f8fafc;
  border: 2px solid #e2e8f0;
  border-radius: 16px;
  overflow: hidden;
  transition: all 0.3s ease;
}

.input-wrapper:focus-within {
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.newsletter-input {
  flex: 1;
  padding: 16px 20px;
  border: none;
  background: transparent;
  color: #1e293b;
  font-size: 1rem;
  outline: none;
}

.newsletter-input::placeholder {
  color: #94a3b8;
}

.newsletter-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 16px 32px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 1rem;
}

.newsletter-btn:hover {
  background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
}

.newsletter-promise {
  color: #94a3b8;
  font-size: 0.9rem;
}

/* About Section */
.about-section {
  padding: 100px 0;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
}

.about-content {
  max-width: 1000px;
  margin: 0 auto;
}

.about-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin-top: 3rem;
}

.about-card {
  background: white;
  padding: 2.5rem;
  border-radius: 20px;
  text-align: center;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.about-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.12);
}

.about-icon {
  width: 60px;
  height: 60px;
  margin: 0 auto 1.5rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.about-icon svg {
  width: 28px;
  height: 28px;
}

.about-card h3 {
  font-size: 1.3rem;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 1rem;
}

.about-card p {
  color: #64748b;
  line-height: 1.6;
  font-size: 1rem;
}

/* Subscription Message Styles */
.subscription-message {
  margin-top: 1rem;
  padding: 12px 16px;
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  border-radius: 8px;
  text-align: center;
}

.subscription-message .success-message {
  color: white;
  margin: 0;
  font-weight: 500;
  font-size: 0.9rem;
}

/* Mobile Menu Styles */
.nav-menu.mobile-active {
  display: flex;
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: rgba(255, 255, 255, 0.98);
  backdrop-filter: blur(20px);
  flex-direction: column;
  padding: 1rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  border-radius: 0 0 16px 16px;
}

.nav-menu.mobile-active .nav-link,
.nav-menu.mobile-active .contact-btn {
  margin: 0.5rem 0;
  text-align: center;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .hero-container {
    grid-template-columns: 1fr;
    text-align: center;
    gap: 3rem;
  }

  .stats-card {
    max-width: 400px;
    margin: 0 auto;
  }
}

@media (max-width: 768px) {
  .nav-menu {
    display: none;
  }

  .nav-menu.mobile-active {
    display: flex;
  }

  .mobile-menu-toggle {
    display: flex;
  }

  .hero-title {
    font-size: 2.5rem;
  }

  .hero-subtitle {
    font-size: 1.1rem;
  }

  .hero-cta {
    flex-direction: column;
    width: 100%;
  }

  .cta-primary,
  .cta-secondary {
    width: 100%;
    justify-content: center;
  }

  .stats-card {
    grid-template-columns: 1fr 1fr;
    gap: 1.5rem;
  }

  .todo-title-section {
    flex-direction: column;
    gap: 1rem;
    align-items: flex-start;
  }

  .task-item {
    padding: 1rem;
  }

  .task-item:not([data-task-id]) {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .task-right {
    align-self: flex-end;
    width: 100%;
    justify-content: space-between;
  }

  .input-wrapper {
    flex-direction: column;
  }

  .newsletter-btn {
    border-radius: 12px;
    margin-top: 0.5rem;
  }

  .newsletter-container {
    padding: 3rem 2rem;
  }

  .todo-container {
    padding: 1.5rem;
  }

  .task-filters {
    justify-content: center;
    flex-wrap: wrap;
  }

  .filter-btn {
    font-size: 0.85rem;
    padding: 6px 12px;
  }

  .about-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .about-card {
    padding: 2rem;
  }
}

@media (max-width: 480px) {
  .container {
    padding: 0 16px;
  }

  .nav-container {
    padding: 0 16px;
  }

  .hero-container {
    padding: 0 16px;
  }

  .hero-title {
    font-size: 2rem;
  }

  .stats-card {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .todo-container {
    padding: 1.5rem;
  }

  .task-filters {
    gap: 0.5rem;
  }

  .filter-btn {
    font-size: 0.8rem;
    padding: 6px 12px;
  }

  .newsletter-container {
    padding: 2rem 1.5rem;
  }
}

/* Smooth Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.task-item {
  animation: fadeInUp 0.6s ease forwards;
}

.task-item:nth-child(2) {
  animation-delay: 0.1s;
}

.task-item:nth-child(3) {
  animation-delay: 0.2s;
}

.task-item:nth-child(4) {
  animation-delay: 0.3s;
}

/* Enhanced Task Item Styles */
.task-item[data-task-id] {
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.task-item[data-task-id]::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(102, 126, 234, 0.1), transparent);
  transition: left 0.5s ease;
}

.task-item[data-task-id]:hover::before {
  left: 100%;
}

.task-text {
  transition: color 0.3s ease;
}

.task-item:hover .task-text {
  color: #667eea;
}

/* Loading Animation */
.loading {
  position: relative;
  overflow: hidden;
}

.loading::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  animation: loading-shimmer 1.5s infinite;
}

@keyframes loading-shimmer {
  0% { left: -100%; }
  100% { left: 100%; }
}

/* Enhanced Button Styles */
.cta-primary, .cta-secondary, .contact-btn, .newsletter-btn {
  position: relative;
  overflow: hidden;
}

/* Toast Enhancements */
.toast {
  pointer-events: auto;
  cursor: pointer;
  position: relative;
}

.toast:hover {
  transform: translateX(-5px) !important;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
}

::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Smooth transitions for all interactive elements */
* {
  transition: color 0.3s ease, background-color 0.3s ease, border-color 0.3s ease, transform 0.3s ease, box-shadow 0.3s ease;
}

/* Additional styles for task management */
.task-form {
  background: white;
  padding: 2rem;
  border-radius: 16px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
  margin-bottom: 2rem;
}

.task-input-wrapper {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.task-input {
  flex: 1;
  padding: 12px 16px;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  font-size: 1rem;
  transition: all 0.3s ease;
}

.task-input:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.tasks-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.task-item {
  background: #fafbfc;
  border: 1px solid #f1f5f9;
  border-radius: 12px;
  padding: 1rem;
  margin-bottom: 0.75rem;
  display: flex;
  align-items: center;
  gap: 1rem;
  transition: all 0.3s ease;
}

.task-item:hover {
  background: #f8fafc;
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
}

.task-item.completed {
  opacity: 0.7;
  background: #f0fdf4;
  border-color: #bbf7d0;
}

.task-item.completed .task-text {
  text-decoration: line-through;
  color: #6b7280;
}

.task-status {
  width: 20px;
  height: 20px;
  cursor: pointer;
}

.task-text {
  flex: 1;
  font-weight: 500;
  color: #1e293b;
}

.delete-task {
  background: #ef4444;
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 0.875rem;
  transition: all 0.3s ease;
}

.delete-task:hover {
  background: #dc2626;
  transform: scale(1.05);
}

.loading {
  opacity: 0.6;
  pointer-events: none;
}