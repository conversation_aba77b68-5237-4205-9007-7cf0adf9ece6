<?php
require_once 'functions.php';

echo "=== Testing Stats Functionality ===\n\n";

// Add some test data
echo "Adding test tasks...\n";
addTask("Complete project documentation");
addTask("Review code changes");
addTask("Update deployment scripts");
addTask("Test email functionality");
addTask("Write user manual");

// Mark some as completed
$tasks = getAllTasks();
if (count($tasks) >= 2) {
    markTaskAsCompleted($tasks[0]['id'], true);
    markTaskAsCompleted($tasks[1]['id'], true);
    echo "Marked 2 tasks as completed\n";
}

// Add some subscribers
$subscribers = getSubscribers();
if (!in_array('<EMAIL>', $subscribers)) {
    $subscribers[] = '<EMAIL>';
}
if (!in_array('<EMAIL>', $subscribers)) {
    $subscribers[] = '<EMAIL>';
}
if (!in_array('<EMAIL>', $subscribers)) {
    $subscribers[] = '<EMAIL>';
}
file_put_contents(__DIR__ . '/subscribers.txt', json_encode($subscribers, JSON_PRETTY_PRINT));

echo "Added test subscribers\n\n";

// Get final stats
$allTasks = getAllTasks();
$allSubscribers = getSubscribers();
$completedTasks = array_filter($allTasks, function($task) { return $task['completed']; });
$pendingTasks = array_filter($allTasks, function($task) { return !$task['completed']; });

echo "=== Current Statistics ===\n";
echo "Total Tasks: " . count($allTasks) . "\n";
echo "Completed Tasks: " . count($completedTasks) . "\n";
echo "Pending Tasks: " . count($pendingTasks) . "\n";
echo "Active Users: " . count($allSubscribers) . "\n\n";

echo "✓ Stats test completed!\n";
echo "Visit http://localhost:8000/index.php to see the updated stats\n";
?>
